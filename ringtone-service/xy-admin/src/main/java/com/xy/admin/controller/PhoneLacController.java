package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.entity.PhoneLac;
import com.xy.admin.service.PhoneLacService;
import com.xy.admin.vo.phoneLac.PhoneLacAddVO;
import com.xy.admin.vo.phoneLac.PhoneLacEditVO;
import com.xy.admin.vo.phoneLac.PhoneLacQueryVO;
import com.xy.admin.vo.phoneLac.PhoneLacVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 手机号段控制层
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
@RestController
@RequestMapping("/phone_lac")
public class PhoneLacController {

    @Autowired
    private PhoneLacService phoneLacService;

    /**
     * 手机号段列表查询接口
     * 支持多字段查询、分页和排序
     */
    @PostMapping("/list")
    @DataSource("tertiary")
    public ResponseEntity<IPage<PhoneLacVO>> list(PhoneLacQueryVO queryVO) {
        IPage<PhoneLacVO> result = phoneLacService.queryPage(queryVO);
        return ResponseEntity.ok(result);
    }

    /**
     * 单个手机号段查询接口（通过号段）
     */
    @GetMapping("/{seg}")
    @DataSource("tertiary")
    public ResponseEntity<PhoneLacVO> getBySeg(@PathVariable String seg) {
        PhoneLac entity = phoneLacService.getById(seg);
        if (entity == null) {
            return ResponseEntity.notFound().build();
        }

        PhoneLacVO vo = new PhoneLacVO();
        BeanUtils.copyProperties(entity, vo);
        return ResponseEntity.ok(vo);
    }

    /**
     * 手机号段新增接口
     */
    @PostMapping
    @DataSource("tertiary")
    public ResponseEntity<String> add(@Valid @RequestBody PhoneLacAddVO addVO) {
        PhoneLac entity = new PhoneLac();
        BeanUtils.copyProperties(addVO, entity);

        boolean success = phoneLacService.save(entity);
        if (success) {
            return ResponseEntity.ok("新增成功");
        } else {
            return ResponseEntity.badRequest().body("新增失败");
        }
    }

    /**
     * 手机号段修改接口
     */
    @PutMapping("/{seg}")
    @DataSource("tertiary")
    public ResponseEntity<String> update(@PathVariable String seg, @Valid @RequestBody PhoneLacEditVO editVO) {
        // 确保号段一致
        editVO.setSeg(seg);

        PhoneLac entity = new PhoneLac();
        BeanUtils.copyProperties(editVO, entity);

        boolean success = phoneLacService.updateById(entity);
        if (success) {
            return ResponseEntity.ok("修改成功");
        } else {
            return ResponseEntity.badRequest().body("修改失败");
        }
    }

    /**
     * 手机号段删除接口
     */
    @DeleteMapping("/{seg}")
    @DataSource("tertiary")
    public ResponseEntity<String> delete(@PathVariable String seg) {
        boolean success = phoneLacService.removeById(seg);
        if (success) {
            return ResponseEntity.ok("删除成功");
        } else {
            return ResponseEntity.badRequest().body("删除失败");
        }
    }
}
